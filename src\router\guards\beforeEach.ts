import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import AppConfig from '@/config'
import auth from '@/plugins/auth'
import usePermissionStore from '@/store/modules/permission'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import { isHttp } from '@/utils/validate'
import { setWorktab } from '@/utils/navigation/worktab'
import { getLang } from '@/locales'
import Home from '@views/index/index.vue'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置进度条
NProgress.configure({
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 白名单路由（无需登录即可访问）
const WHITE_LIST = ['/login', '/register', '/otherLogCallback', '/upload'] as const

// 默认重定向路由
const DEFAULT_ROUTE = '/dashboard/console'

/**
 * 检查路由是否在白名单中
 */
const isInWhiteList = (path: string): boolean => {
  return WHITE_LIST.includes(path as any)
}

/**
 * 检查权限
 */
const checkPermission = (perms: string | string[]): boolean => {
  return auth.hasPermi(perms)
}

/**
 * 处理用户信息获取失败的情况
 */
const handleUserInfoError = async (error: any): Promise<void> => {
  console.error('获取用户信息失败:', error)
  try {
    await useUserStore().logOut()
  } catch (logoutError) {
    console.error('登出失败:', logoutError)
  }
}

/**
 * 生成并添加动态路由
 */
const generateAndAddRoutes = async (router: Router): Promise<void> => {
  const accessRoutes = await usePermissionStore().generateRoutes()

  // 动态添加可访问路由表
  accessRoutes.forEach((route) => {
    if (!isHttp(route.path)) {
      route.component = Home
      router.addRoute(route)
    }
  })
}

/**
 * 设置页面标题和标签页
 */
const setPageTitleAndTab = (to: RouteLocationNormalized): void => {
  const { meta, path, params, query } = to
  const { menuCode, notTab, noCache } = meta

  // 设置标签页
  if (!notTab && menuCode) {
    setWorktab({
      title: menuCode as string,
      path,
      name: menuCode as string,
      keepAlive: noCache as boolean,
      params,
      query,
      meta
    })
  }

  // 设置网页标题
  if (menuCode) {
    document.title = `${getLang(menuCode as string)} - ${AppConfig.systemInfo.name}`
  } else {
    document.title = AppConfig.systemInfo.name
  }
}

/**
 * 处理已登录用户的路由导航
 */
const handleAuthenticatedUser = async (
  to: RouteLocationNormalized,
  next: NavigationGuardNext,
  router: Router
): Promise<void> => {
  // 如果访问登录页，重定向到首页
  if (to.path === '/login') {
    next({ path: DEFAULT_ROUTE })
    NProgress.done()
    return
  }

  // 如果在白名单中，直接通过
  if (isInWhiteList(to.path)) {
    next()
    return
  }

  // 检查菜单列表是否已加载
  if (usePermissionStore().menuList.length === 0) {
    isRelogin.show = true

    try {
      // 获取用户信息（登录后首次获取，强制重新加载国际化数据）
      await useUserStore().getInfo(true)
      isRelogin.show = false

      // 生成并添加路由
      await generateAndAddRoutes(router)

      // 重新导航到目标路由
      next({ ...to, replace: true })
    } catch (error) {
      isRelogin.show = false
      await handleUserInfoError(error)
      next({ path: '/login' })
      NProgress.done()
    }
  } else {
    // 处理刷新页面导致的路由丢失问题
    if (to.matched.length === 0) {
      try {
        await generateAndAddRoutes(router)
        next({ ...to, replace: true })
      } catch (error) {
        console.error('重新生成路由失败:', error)
        next({ path: DEFAULT_ROUTE })
      }
    } else {
      next()
    }
  }
}

/**
 * 处理未登录用户的路由导航
 */
const handleUnauthenticatedUser = (
  to: RouteLocationNormalized,
  next: NavigationGuardNext
): void => {
  // 初始化国际化数据（优先使用缓存）
  useUserStore().initI18nMessages()

  if (isInWhiteList(to.path)) {
    // 在白名单中，直接进入
    next()
  } else {
    // 重定向到登录页，保存完整路径
    const redirectPath = to.fullPath === '/' ? DEFAULT_ROUTE : to.fullPath
    next(`/login?redirect=${encodeURIComponent(redirectPath)}`)
    NProgress.done()
  }
}

/**
 * 设置路由全局前置守卫
 */
export function setupBeforeEachGuard(router: Router): void {
  router.beforeEach(async (to, from, next) => {
    NProgress.start()

    try {
      const { meta } = to
      const { perms } = meta

      // 权限检查
      if (perms && !checkPermission(perms)) {
        next({ path: '/exception/403' })
        NProgress.done()
        return
      }

      const token = getToken()

      if (token) {
        // 已登录用户处理
        await handleAuthenticatedUser(to, next, router)
      } else {
        // 未登录用户处理
        handleUnauthenticatedUser(to, next)
      }

      // 设置页面标题和标签页
      setPageTitleAndTab(to)
    } catch (error) {
      console.error('路由导航错误:', error)
      NProgress.done()
      next('/login')
    }
  })
}

/**
 * 重置路由相关状态
 */
export function resetRouterState(): void {
  // 重置权限store的状态
  const permissionStore = usePermissionStore()
  permissionStore.menuList = []

  // 重置菜单store的状态
  const menuStore = useMenuStore()
  menuStore.removeAllDynamicRoutes()
  menuStore.setMenuList([])

  console.log('路由状态已重置')
}
