<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ac5e7402-2dde-4f2a-b962-fd6c2fac2a63" name="Changes" comment="重构">
      <change beforePath="$PROJECT_DIR$/src/api/system/activitiModel/ActivitiModel.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/productCategories/ProductCategories.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/products/Products.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/share/Share.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/shareReceive/ShareReceive.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/tGiftBooks/TGiftBooks.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/tGiftRecords/TGiftRecords.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/todo/Todo.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/wechatUser/WechatUser.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/weddingPhotos/WeddingPhotos.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/system/work/Work.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/activitiModel/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/activitiModel/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/activitiModel/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftBooks/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftBooks/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftBooks/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftRecords/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftRecords/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/giftRecords/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/productCategories/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/productCategories/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/productCategories/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/products/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/products/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/products/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/share/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/share/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/share/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/shareReceive/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/shareReceive/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/shareReceive/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/todo/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/todo/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/todo/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/wechatUser/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/wechatUser/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/wechatUser/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/weddingPhotos/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/weddingPhotos/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/weddingPhotos/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/work/components/add.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/work/components/bigtian.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/work/components/detail.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/work/components/other.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/work/index.vue" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-icon" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30JwN1QEOh6zOm16hj4FJ0UShUY" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "common-project",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.standard": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.standard": "",
    "node.js.selected.package.stylelint": "D:\\code\\art-design-pro\\node_modules\\stylelint",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "prettierjs.PrettierConfiguration.Package": "D:\\code\\art-design-pro\\node_modules\\prettier",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "D:\\code\\art-design-pro\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.361" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ac5e7402-2dde-4f2a-b962-fd6c2fac2a63" name="Changes" comment="" />
      <created>1753358939937</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753358939937</updated>
      <workItem from="1753358941037" duration="3967000" />
      <workItem from="1753362950193" duration="7251000" />
      <workItem from="1753444344795" duration="782000" />
      <workItem from="1753445140787" duration="371000" />
      <workItem from="1753445521009" duration="17375000" />
      <workItem from="1753491544321" duration="4569000" />
      <workItem from="1753548151253" duration="215000" />
      <workItem from="1753605939974" duration="11469000" />
      <workItem from="1753702536470" duration="5954000" />
      <workItem from="1753878115403" duration="126000" />
      <workItem from="1753878258852" duration="978000" />
      <workItem from="1753881633284" duration="211000" />
    </task>
    <task id="LOCAL-00001" summary="重构">
      <option name="closed" value="true" />
      <created>1753370207656</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753370207656</updated>
    </task>
    <task id="LOCAL-00002" summary="重构">
      <option name="closed" value="true" />
      <created>1753450368601</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753450368601</updated>
    </task>
    <task id="LOCAL-00003" summary="重构">
      <option name="closed" value="true" />
      <created>1753456502149</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753456502149</updated>
    </task>
    <task id="LOCAL-00004" summary="重构">
      <option name="closed" value="true" />
      <created>1753457183304</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753457183304</updated>
    </task>
    <task id="LOCAL-00005" summary="重构">
      <option name="closed" value="true" />
      <created>1753458631271</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753458631271</updated>
    </task>
    <task id="LOCAL-00006" summary="重构">
      <option name="closed" value="true" />
      <created>1753460429740</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753460429740</updated>
    </task>
    <task id="LOCAL-00007" summary="重构">
      <option name="closed" value="true" />
      <created>1753464039070</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753464039070</updated>
    </task>
    <task id="LOCAL-00008" summary="重构">
      <option name="closed" value="true" />
      <created>1753494810698</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753494810698</updated>
    </task>
    <task id="LOCAL-00009" summary="重构">
      <option name="closed" value="true" />
      <created>1753495433461</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753495433461</updated>
    </task>
    <task id="LOCAL-00010" summary="重构">
      <option name="closed" value="true" />
      <created>1753611385869</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753611385869</updated>
    </task>
    <task id="LOCAL-00011" summary="重构">
      <option name="closed" value="true" />
      <created>1753618871577</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753618871577</updated>
    </task>
    <task id="LOCAL-00012" summary="重构">
      <option name="closed" value="true" />
      <created>1753625348212</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753625348212</updated>
    </task>
    <task id="LOCAL-00013" summary="重构">
      <option name="closed" value="true" />
      <created>1753626058563</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753626058563</updated>
    </task>
    <task id="LOCAL-00014" summary="重构">
      <option name="closed" value="true" />
      <created>1753626113299</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753626113299</updated>
    </task>
    <task id="LOCAL-00015" summary="重构">
      <option name="closed" value="true" />
      <created>1753708438197</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753708438197</updated>
    </task>
    <option name="localTasksCounter" value="16" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev-icon" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="修复表格" />
    <MESSAGE value="重构" />
    <option name="LAST_COMMIT_MESSAGE" value="重构" />
  </component>
</project>