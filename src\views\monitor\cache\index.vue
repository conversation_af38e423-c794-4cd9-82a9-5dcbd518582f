<template>
  <div class="cache-monitor">
    <el-row :gutter="16">
      <!-- 基础信息卡片 -->
      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <Monitor style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('information.basic') }}</span>
          </template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%">
              <tbody>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('version.redis') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.redis_version }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('mode.operating') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.redis_mode == 'standalone' ? '单机' : '集群' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('port') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.tcp_port }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('clients.number') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.connected_clients }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('time.running.days') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.uptime_in_days }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('memory.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.used_memory_human }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('cpu.using') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('configuration.memory') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell">{{ cache.info.maxmemory_human }}</div>
                  </td>
                </tr>
                <tr>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('aof.enabled') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.aof_enabled == '0' ? '否' : '是' }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('rdb.success') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.rdb_last_bgsave_status }}
                    </div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('key.quantity') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.dbSize" class="cell">{{ cache.dbSize }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div class="cell">{{ $t('network.ingress.egress') }}</div>
                  </td>
                  <td class="el-table__cell is-leaf">
                    <div v-if="cache.info" class="cell"
                      >{{ cache.info.instantaneous_input_kbps }}kps/{{
                        cache.info.instantaneous_output_kbps
                      }}kps
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" style="margin-top: 16px;">
      <!-- 命令统计饼图 -->
      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <PieChart style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('statistics.command') }}</span>
          </template>
          <div class="chart-container">
            <div ref="commandstats" style="height: 420px" />
          </div>
        </el-card>
      </el-col>

      <!-- 内存信息仪表盘 -->
      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Odometer style="width: 1em; height: 1em; vertical-align: middle" />
            <span style="vertical-align: middle">{{ $t('memory.info') }}</span>
          </template>
          <div class="chart-container">
            <div ref="usedmemory" style="height: 420px" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script name="Cache" setup>
  import { getCache } from '@/api/monitor/cache'
  import * as echarts from 'echarts'
  import { getCurrentInstance, computed } from 'vue'
  import { useSettingStore } from '@/store/modules/setting'

  const { proxy } = getCurrentInstance()
  const settingStore = useSettingStore()

  const cache = ref([])
  const commandstats = ref(null)
  const usedmemory = ref(null)

  // 检测当前主题
  const isDark = computed(() => settingStore.isDark)

  // 根据主题获取颜色配置
  const getThemeColors = () => {
    if (isDark.value) {
      return {
        textColor: '#e2e8f0',
        backgroundColor: 'rgba(45, 55, 72, 0.9)',
        borderColor: '#4a5568',
        labelLineColor: '#e2e8f0',
        axisColor: '#e2e8f0'
      }
    } else {
      return {
        textColor: '#333333',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#d1d5db',
        labelLineColor: '#666666',
        axisColor: '#666666'
      }
    }
  }

  function getList(page) {
    if (page) {
      queryParams.value = { ...queryParams.value, ...page }
    }
    proxy.$modal.loading(proxy.$t('data.monitoring.cache.loading'))
    getCache().then((response) => {
      proxy.$modal.closeLoading()
      cache.value = response.data

      const themeColors = getThemeColors()
      const commandstatsIntance = echarts.init(commandstats.value, 'macarons')
      commandstatsIntance.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
          backgroundColor: themeColors.backgroundColor,
          borderColor: themeColors.borderColor,
          borderWidth: 1,
          textStyle: {
            color: themeColors.textColor,
            fontSize: 12
          }
        },
        series: [
          {
            name: proxy.$t('command'),
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: response.data.commandStats,
            animationEasing: 'cubicInOut',
            animationDuration: 1000,
            label: {
              show: true,
              position: 'outside',
              color: themeColors.textColor,
              fontSize: 12,
              fontWeight: 'normal',
              formatter: '{b}',
              textBorderColor: isDark.value ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',
              textBorderWidth: 1
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 8,
              lineStyle: {
                color: themeColors.labelLineColor,
                width: 1
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold',
                color: themeColors.textColor
              }
            }
          }
        ]
      })
      const usedmemoryInstance = echarts.init(usedmemory.value, 'macarons')
      usedmemoryInstance.setOption({
        tooltip: {
          formatter: '{b} <br/>{a} : ' + cache.value.info.used_memory_human,
          backgroundColor: themeColors.backgroundColor,
          borderColor: themeColors.borderColor,
          borderWidth: 1,
          textStyle: {
            color: themeColors.textColor,
            fontSize: 12
          }
        },
        series: [
          {
            name: proxy.$t('peak'),
            type: 'gauge',
            min: 0,
            max: 1000,
            axisLine: {
              lineStyle: {
                width: 8,
                color: [
                  [0.3, '#67C23A'],
                  [0.7, '#E6A23C'],
                  [1, '#F56C6C']
                ]
              }
            },
            axisLabel: {
              color: themeColors.axisColor,
              fontSize: 12
            },
            axisTick: {
              lineStyle: {
                color: themeColors.axisColor
              }
            },
            splitLine: {
              lineStyle: {
                color: themeColors.axisColor
              }
            },
            pointer: {
              itemStyle: {
                color: themeColors.axisColor
              }
            },
            title: {
              color: themeColors.textColor,
              fontSize: 14,
              fontWeight: 'bold'
            },
            detail: {
              formatter: cache.value.info.used_memory_human,
              color: themeColors.textColor,
              fontSize: 16,
              fontWeight: 'bold'
            },
            data: [
              {
                value: parseFloat(cache.value.info.used_memory_human),
                name: proxy.$t('consumption.memory')
              }
            ]
          }
        ]
      })
      window.addEventListener('resize', () => {
        commandstatsIntance.resize()
        usedmemoryInstance.resize()
      })
    })
  }

  getList()
</script>

<style scoped>
.cache-monitor {
  padding: 16px;
}

.card-box {
  margin-bottom: 0;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  height: 100%;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.el-card__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  border-radius: 8px 8px 0 0;
  padding: 16px 20px;
}

.el-card__body {
  padding: 20px;
}

.chart-container {
  width: 100%;
  height: 100%;
}

/* 优化表格样式 */
.el-table__cell {
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: transparent;
}

.el-table__cell .cell {
  padding: 8px 12px;
  font-size: 13px;
  color: var(--el-text-color-primary);
}

/* 图表容器样式 */
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--el-fill-color-light);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .cache-monitor {
    padding: 8px;
  }

  .el-card__body {
    padding: 16px;
  }

  .chart-container div {
    height: 300px !important;
  }
}

/* 深色主题适配 */
.dark .el-card {
  background-color: var(--el-bg-color-page);
  border-color: var(--el-border-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.dark .el-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.4);
}

.dark .el-card__header {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  border-bottom-color: var(--el-border-color);
}

.dark .el-table__cell {
  border-bottom-color: var(--el-border-color);
  color: var(--el-text-color-primary);
}

.dark .el-table__cell .cell {
  color: var(--el-text-color-primary);
}

.dark .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: var(--el-fill-color-dark);
}

/* 浅色主题适配 */
.light .el-card {
  background-color: #ffffff;
  border-color: #ebeef5;
}

.light .el-card__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.light .el-table__cell {
  border-bottom-color: #ebeef5;
  color: #606266;
}

.light .el-table__cell .cell {
  color: #606266;
}
</style>
